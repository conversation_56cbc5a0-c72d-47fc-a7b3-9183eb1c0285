package store

import (
	"database/sql"
	"time"
)

// FollowRequestStore handles database operations for follow request.
type FollowRequestStore struct {
	DB *sql.DB
}

// NewFollowRequestStore creates a new FollowRequestStore.
func NewFollowRequestStore(db *sql.DB) *FollowRequestStore {
	return &FollowRequestStore{DB: db}
}

// AcceptFollowConnection accepts a follow request by setting is_accepted to true and updating accepted_at
func (fr *FollowRequestStore) AcceptFollowConnection(followConnectionID int64) error {
	query := "UPDATE Followers SET is_accepted = 1, accepted_at = ? WHERE id = ? AND is_accepted = 0"
	_, err := fr.DB.Exec(query, time.Now(), followConnectionID)
	return err
}

// RejectFollowConnection rejects a follow request by deleting the record
func (fr *FollowRequestStore) RejectFollowConnection(followConnectionID int64) error {
	query := "DELETE FROM Followers WHERE id = ? AND is_accepted = 0"
	_, err := fr.DB.Exec(query, followConnectionID)
	return err
}
