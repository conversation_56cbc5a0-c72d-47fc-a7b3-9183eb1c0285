package store

import "database/sql"

// FollowRequestStore handles database operations for unfollow.
type FollowRequestStore struct {
	DB *sql.DB
}

// NewFollowRequestStore creates a new FollowRequestStore.
func NewFollowRequestStore(db *sql.DB) *FollowRequestStore {
	return &FollowRequestStore{DB: db}
}

func (fr *FollowRequestStore) AcceptedFollowConnection(followConnectionID int64) error {
	return nil
}

func (fr *FollowRequestStore) RejectedFollowConnection(followConnectionID int64) error {
	return nil
}
