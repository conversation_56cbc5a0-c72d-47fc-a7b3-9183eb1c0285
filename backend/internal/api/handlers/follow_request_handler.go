package handlers

import (
	"encoding/json"
	"io"
	"net/http"
	"strconv"

	"github.com/tajjjjr/social-network/backend/internal/models"
	"github.com/tajjjjr/social-network/backend/internal/service"
	"github.com/tajjjjr/social-network/backend/pkg/utils"
)

type FollowRequestHandler struct {
	FollowRequestService service.FollowRequestServiceInterface
}

func NewFollowRequestHandler(fr service.FollowRequestServiceInterface) *FollowRequestHandler {
	return &FollowRequestHandler{FollowRequestService: fr}
}

func (fr *FollowRequestHandler) FollowRequestRespond(w http.ResponseWriter, r *http.Request) {
	requestIDStr := r.PathValue("requestId")
	requestID, err := strconv.ParseInt(requestIDStr, 10, 64)
	if err != nil {
		utils.RespondJSON(w, http.StatusBadRequest, utils.Response{Message: "Invalid request ID"})
		return
	}

	var status models.FollowRequestResponseStatus

	body, err := io.ReadAll(r.Body)
	if err != nil {
		utils.RespondJSON(w, http.StatusBadRequest, utils.Response{Message: "Invalid request body"})
		return
	}
	err = json.Unmarshal(body, &status)
	if err != nil {
		utils.RespondJSON(w, http.StatusBadRequest, utils.Response{Message: "Invalid request body"})
		return
	}

	if status.Status == "Rejected" {
		err = fr.FollowRequestService.RejectedFollowConnection(requestID)
		if err != nil {
			utils.RespondJSON(w, http.StatusInternalServerError, utils.Response{Message: "Failed to reject follow request"})
		}
		utils.RespondJSON(w, http.StatusOK, utils.Response{Message: "Successfully rejected follow request"})
		return
	}

	err = fr.FollowRequestService.AcceptedFollowConnection(requestID)
	if err != nil {
		utils.RespondJSON(w, http.StatusInternalServerError, utils.Response{Message: "Failed to accept follow request"})
		return
	}
	utils.RespondJSON(w, http.StatusOK, utils.Response{Message: "Successfully accepted follow request"})
}
