package service

import "github.com/tajjjjr/social-network/backend/internal/store"

type FollowRequestService struct {
	FollowRequestService *store.FollowRequestStore
}

func NewFollowRequestService(fr *store.FollowRequestStore) *FollowRequestService {
	return &FollowRequestService{FollowRequestService: fr}
}

func (fr *FollowRequestService) AcceptedFollowConnection(followConnectionID int64) error {
	return nil
}

func (fr *FollowRequestService) RejectedFollowConnection(followConnectionID int64) error {
	return nil
}
