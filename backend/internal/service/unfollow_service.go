package service

import "github.com/tajjjjr/social-network/backend/internal/store"

type UnfollowService struct {
	FollowStore *store.UnfollowStore
}

func NewUnfollowService(ff *store.UnfollowStore) *UnfollowService {
	return &UnfollowService{FollowStore: ff}
}

func (unf *UnfollowService) GetFollowConnectionID(followerID, followeeID int64) (int64, error) {
	return  0,nil
}

func (unf *UnfollowService) DeleteFollowConnection(followConnectionID int64) error {
	return nil
}
