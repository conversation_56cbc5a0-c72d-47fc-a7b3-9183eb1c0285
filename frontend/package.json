{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"lint": "next lint", "test": "jest --passWithNoTests", "build": "next build", "start": "next start", "serve:next": "dotenv -e .env -- sh -c 'PORT=$NEXT_PORT next dev'", "serve:go": "dotenv -e .env -- sh -c 'cd ../backend && go run .'", "dev": "concurrently -k -n go,next -c yellow,cyan \"npm run serve:go\" \"npm run serve:next\""}, "dependencies": {"lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "socket.io-client": "^4.8.1", "ws": "^8.18.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "concurrently": "^9.2.0", "dotenv-cli": "^8.0.0", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}